name: CI/CD for Python - Production
run-name: Production Deployment of [${{ inputs.serviceName }}] by [@${{ github.actor }}]
on:
  workflow_dispatch:
    inputs:
      serviceName:
        description: 'Service to deploy'
        required: true
        type: choice
        options:
          - math-agent
          - orchestrator-agent

env:
  AWS_REGION: ${{ secrets.AWS_REGION }}
  ECR_REGISTRY: ${{ secrets.ECR_REGISTRY }}
  ECR_REPOSITORY: ${{ secrets.ECR_REPO }}
  ECS_CLUSTER: ${{ secrets.ECS_CLUSTER_NAME_PROD }}
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  ENVIRONMENT: 'prod'
  BRANCH: 'main'

jobs:
  deploy-on-command:
    runs-on: ubuntu-latest
    steps:
      - name: Definir SERVICE_NAME
        run: |
          echo "SERVICE_NAME=${{ github.event.inputs.serviceName }}" >> $GITHUB_ENV

      - name: Checkout do reposit<PERSON><PERSON>
        uses: actions/checkout@v4
        with:
          ref: ${{ env.BRANCH }}

      - name: Configurar credenciais AWS
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login no Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build e push da imagem Docker
        run: |
          SERVICE_PATH=${{ env.SERVICE_NAME }}
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          IMAGE_TAG=${{ env.ENVIRONMENT }}-${{ github.sha }}-$TIMESTAMP 
          IMAGE_URI=${{ env.ECR_REGISTRY }}/${{ env.SERVICE_NAME }}:$IMAGE_TAG
          echo "Building Docker image $IMAGE_URI"
          docker build -t $IMAGE_URI -f $SERVICE_PATH/Dockerfile $SERVICE_PATH
          docker push $IMAGE_URI
          echo "IMAGE_URI=$IMAGE_URI" >> $GITHUB_ENV

      - name: Atualizar a task definition do Amazon ECS
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.SERVICE_NAME }}/ecs/${{ env.ENVIRONMENT }}/task-definition.json
          container-name: ${{ env.SERVICE_NAME }}-${{ env.ENVIRONMENT }}
          image: ${{ env.IMAGE_URI }}

      - name: Deploy da task definition no Amazon ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.SERVICE_NAME }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: false

      - name: Verify Deployment
        if: env.SERVICE_NAME
        uses: ./.github/actions/verify-deployment
        with:
          image-uri: ${{ env.IMAGE_URI }}
          service-name: ${{ env.SERVICE_NAME }}-${{ env.ENVIRONMENT }}
          cluster: ${{ env.ECS_CLUSTER }}
          timeout: '900'  # 15-minute timeout
