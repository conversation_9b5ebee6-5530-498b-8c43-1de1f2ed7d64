{"family": "math-agent", "taskRoleArn": "arn:aws:iam::992382535149:role/ecs-tasks", "executionRoleArn": "arn:aws:iam::992382535149:role/ecs-tasks", "networkMode": "bridge", "containerDefinitions": [{"name": "math-agent", "image": "992382535149.dkr.ecr.us-east-1.amazonaws.com/math-agent:staging", "memoryReservation": 1024, "memory": 1024, "portMappings": [{"containerPort": 10002, "protocol": "tcp"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/shared/secrets-7NSTZq:DD_API_KEY::"}, {"name": "GOOGLE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/math-agent/secrets-EH97ca:GOOGLE_API_KEY::"}, {"name": "GOOGLE_GENAI_USE_VERTEXAI", "valueFrom": "arn:aws:secretsmanager:us-east-1:992382535149:secret:staging/math-agent/secrets-EH97ca:GOOGLE_GENAI_USE_VERTEXAI::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/staging/math-agent", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "DD_ENV", "value": "new-staging"}, {"name": "DD_SERVICE", "value": "math-agent"}, {"name": "DD_VERSION", "value": "1.0"}], "dockerLabels": {"com.datadoghq.ad.logs": "[{\"source\": \"python\", \"service\": \"math-agent\"}]", "com.datadoghq.tags.env": "staging", "com.datadoghq.tags.service": "math-agent", "com.datadoghq.tags.version": "1.0"}}], "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "memory": "1024"}