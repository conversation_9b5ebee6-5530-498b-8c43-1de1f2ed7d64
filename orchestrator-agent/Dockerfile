FROM python:3.13-slim

# Set the work directory
WORKDIR /app

# Copy all dependency definition files and source code first
COPY orchestrator-agent .

# Install the 'uv' package manager
RUN pip install uv

# Install exact dependencies as specified in uv.lock
# fail if there's a mismatch with pyproject.toml
RUN uv sync --frozen

# Expose the port the application runs on
EXPOSE 10000

# Command to run the application using ADK web command
CMD ["uv", "run", "adk", "web", "src", "--host", "0.0.0.0", "--port", "10000"]
