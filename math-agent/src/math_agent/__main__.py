import logging
import os

import uvicorn
from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import <PERSON><PERSON>ultRequestH<PERSON>ler
from a2a.server.tasks import InMemoryTaskStore
from a2a.types import (
    AgentCapabilities,
    AgentCard,
    AgentSkill,
)
from agent import create_agent
from dotenv import load_dotenv
from google.adk.artifacts import InMemoryArtifactService
from google.adk.memory.in_memory_memory_service import InMemoryMemoryService
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from starlette.responses import JSONResponse
from starlette.routing import Route

from commons.agent_executor import RunnerAgentExecutor

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MissingAPIKeyError(Exception):
    """Exception for missing API key."""

    pass


async def health_check(request):
    """Health check endpoint."""
    return JSONResponse({"status": "healthy", "service": "math-agent"})


def main():
    """Starts the agent server."""
    host = "0.0.0.0"
    port = 10002
    try:
        # Check for API key only if Vertex AI is not configured
        if not os.getenv("GOOGLE_GENAI_USE_VERTEXAI") == "TRUE":
            if not os.getenv("GOOGLE_API_KEY"):
                raise MissingAPIKeyError(
                    "GOOGLE_API_KEY environment variable not set and GOOGLE_GENAI_USE_VERTEXAI is not TRUE."
                )

        capabilities = AgentCapabilities(streaming=True)

        skill = AgentSkill(
            id="math_agent",
            name="Math Agent",
            description="An agent that performs mathematical calculations and resolves scheduling conflicts that require arithmetic or logic.",
            tags=["math", "scheduling"],
            examples=["What is 10 + 10?"],
        )

        agent_card = AgentCard(
            name="Math Agent",
            description="An agent that performs mathematical calculations and resolves scheduling conflicts that require arithmetic or logic.",
            url=os.getenv("MATH_AGENT_URI", "http://localhost:10002"),
            version="1.0.0",
            defaultInputModes=["text/plain"],
            defaultOutputModes=["text/plain"],
            capabilities=capabilities,
            skills=[skill],
        )

        adk_agent = create_agent()
        runner = Runner(
            app_name=agent_card.name,
            agent=adk_agent,
            artifact_service=InMemoryArtifactService(),
            session_service=InMemorySessionService(),
            memory_service=InMemoryMemoryService(),
        )

        agent_executor = RunnerAgentExecutor(runner)

        request_handler = DefaultRequestHandler(
            agent_executor=agent_executor,
            task_store=InMemoryTaskStore(),
        )
        server = A2AStarletteApplication(
            agent_card=agent_card, http_handler=request_handler
        )

        # Add health check route
        health_route = Route("/health", health_check, methods=["GET"])
        server.add_routes_to_app([health_route])

        uvicorn.run(server.build(), host=host, port=port)
    except MissingAPIKeyError as e:
        logger.error(f"Error: {e}")
        exit(1)
    except Exception as e:
        logger.error(f"An error occurred during server startup: {e}")
        exit(1)


if __name__ == "__main__":
    main()
