[project]
name = "math-agent"
version = "0.1.0"
description = "A specialized agent for performing mathematical computations and solving math-related queries in the A2A project."
requires-python = "~=3.13"
dependencies = [
    "a2a-sdk==0.2.5",
    "google-adk==1.2.1",
    "python-dotenv~=1.1.1",
    "uvicorn~=0.35.0",
    "commons",
] 

[tool.uv.workspace]
members = ["../commons"]

[tool.uv.sources]
commons = { workspace = true }