FROM python:3.13-slim

# Copy all dependecies for the agent
COPY commons ./commons

# Set the work directory
WORKDIR /app

# Copy all dependency definition files and source code first
COPY magie-wrapper .

# Install the 'uv' package manager
RUN pip install uv

# Install exact dependencies as specified in uv.lock
# fail if there's a mismatch with pyproject.toml
RUN uv sync --frozen

# Copy and make entrypoint script executable
COPY magie-wrapper/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Expose the port the application runs on
EXPOSE 10003

# Use entrypoint script to conditionally run different commands
ENTRYPOINT ["/app/entrypoint.sh"]
